// Conditional Logic Types for Form Questions

export type ConditionalOperator =
  | "equals"
  | "not_equals"
  | "contains"
  | "not_contains"
  | "greater_than"
  | "less_than"
  | "greater_than_or_equal"
  | "less_than_or_equal"
  | "is_empty"
  | "is_not_empty"

export type ConditionalLogicRule = {
  id: string
  questionId: string // The question to check
  operator: ConditionalOperator
  value: string | number | boolean | string[] // The value to compare against
}

export type ConditionalLogicGroup = {
  id: string
  rules: ConditionalLogicRule[]
  logic: "AND" | "OR" // How to combine rules within this group
}

export type ShowConditions = {
  groups: ConditionalLogicGroup[]
  groupLogic: "AND" | "OR" // How to combine groups
}

// Helper types for form responses
export type FormResponseValue = {
  textValue?: string
  numberValue?: number
  booleanValue?: boolean
  dateValue?: Date
  selectedOptions?: string[]
}

// Utility functions for conditional logic evaluation
export const evaluateRule = (rule: ConditionalLogicRule, responseValue: FormResponseValue | undefined): boolean => {
  const { operator, value } = rule

  // Handle empty/not empty checks first
  if (operator === "is_empty") {
    return (
      !responseValue ||
      Object.values(responseValue).every(
        (v) => v === undefined || v === null || v === "" || (Array.isArray(v) && v.length === 0)
      )
    )
  }

  if (operator === "is_not_empty") {
    return (
      !!responseValue &&
      Object.values(responseValue).some(
        (v) => v !== undefined && v !== null && v !== "" && (!Array.isArray(v) || v.length > 0)
      )
    )
  }

  // For other operators, we need a response value
  if (!responseValue) return false

  // Get the actual value to compare
  let actualValue: string | number | boolean | string[] | undefined

  if (responseValue.textValue !== undefined) actualValue = responseValue.textValue
  else if (responseValue.numberValue !== undefined) actualValue = responseValue.numberValue
  else if (responseValue.booleanValue !== undefined) actualValue = responseValue.booleanValue
  else if (responseValue.selectedOptions !== undefined) actualValue = responseValue.selectedOptions
  else if (responseValue.dateValue !== undefined) actualValue = responseValue.dateValue.toISOString()

  if (actualValue === undefined) return false

  // Evaluate based on operator
  switch (operator) {
    case "equals":
      if (Array.isArray(actualValue) && Array.isArray(value)) {
        return actualValue.length === value.length && actualValue.every((v) => value.includes(v))
      }

      // Special handling for single choice questions - compare array with single value
      if (Array.isArray(actualValue) && !Array.isArray(value)) {
        return actualValue.length === 1 && actualValue[0] === value
      }
      if (!Array.isArray(actualValue) && Array.isArray(value)) {
        return value.length === 1 && value[0] === actualValue
      }

      // Special handling for boolean values - convert string values to boolean for comparison
      if (typeof actualValue === "boolean" && typeof value === "string") {
        const boolValue = value.toLowerCase() === "true"
        return actualValue === boolValue
      }
      if (typeof value === "boolean" && typeof actualValue === "string") {
        const boolValue = actualValue.toLowerCase() === "true"
        return boolValue === value
      }

      return actualValue === value

    case "not_equals":
      if (Array.isArray(actualValue) && Array.isArray(value)) {
        return !(actualValue.length === value.length && actualValue.every((v) => value.includes(v)))
      }

      // Special handling for single choice questions - compare array with single value
      if (Array.isArray(actualValue) && !Array.isArray(value)) {
        return !(actualValue.length === 1 && actualValue[0] === value)
      }
      if (!Array.isArray(actualValue) && Array.isArray(value)) {
        return !(value.length === 1 && value[0] === actualValue)
      }

      // Special handling for boolean values - convert string values to boolean for comparison
      if (typeof actualValue === "boolean" && typeof value === "string") {
        const boolValue = value.toLowerCase() === "true"
        return actualValue !== boolValue
      }
      if (typeof value === "boolean" && typeof actualValue === "string") {
        const boolValue = actualValue.toLowerCase() === "true"
        return boolValue !== value
      }

      return actualValue !== value

    case "contains":
      if (Array.isArray(actualValue)) {
        return Array.isArray(value) ? value.some((v) => actualValue.includes(v)) : actualValue.includes(String(value))
      }
      return String(actualValue).toLowerCase().includes(String(value).toLowerCase())

    case "not_contains":
      if (Array.isArray(actualValue)) {
        return Array.isArray(value) ? !value.some((v) => actualValue.includes(v)) : !actualValue.includes(String(value))
      }
      return !String(actualValue).toLowerCase().includes(String(value).toLowerCase())

    case "greater_than":
      return Number(actualValue) > Number(value)

    case "less_than":
      return Number(actualValue) < Number(value)

    case "greater_than_or_equal":
      return Number(actualValue) >= Number(value)

    case "less_than_or_equal":
      return Number(actualValue) <= Number(value)

    default:
      return false
  }
}

export const evaluateGroup = (group: ConditionalLogicGroup, responses: Record<string, FormResponseValue>): boolean => {
  const results = group.rules.map((rule) => evaluateRule(rule, responses[rule.questionId]))

  return group.logic === "AND" ? results.every((result) => result) : results.some((result) => result)
}

export const evaluateShowConditions = (
  showConditions: ShowConditions,
  responses: Record<string, FormResponseValue>
): boolean => {
  if (!showConditions.groups.length) return true

  const groupResults = showConditions.groups.map((group) => evaluateGroup(group, responses))

  return showConditions.groupLogic === "AND"
    ? groupResults.every((result) => result)
    : groupResults.some((result) => result)
}

// Helper to get available operators for a question type
export const getAvailableOperators = (questionType: string): ConditionalOperator[] => {
  const baseOperators: ConditionalOperator[] = ["equals", "not_equals", "is_empty", "is_not_empty"]

  switch (questionType) {
    case "TEXT_SHORT":
    case "TEXT_LONG":
    case "EMAIL":
      return [...baseOperators, "contains", "not_contains"]

    case "NUMBER":
    case "RATING":
      return [...baseOperators, "greater_than", "less_than", "greater_than_or_equal", "less_than_or_equal"]

    case "SINGLE_CHOICE":
    case "MULTIPLE_CHOICE":
      return [...baseOperators, "contains", "not_contains"]

    case "YES_NO":
    case "DATE":
      return baseOperators

    default:
      return baseOperators
  }
}

// Helper to get operator display name
export const getOperatorDisplayName = (operator: ConditionalOperator): string => {
  const names: Record<ConditionalOperator, string> = {
    equals: "est égal à",
    not_equals: "n'est pas égal à",
    contains: "contient",
    not_contains: "ne contient pas",
    greater_than: "est supérieur à",
    less_than: "est inférieur à",
    greater_than_or_equal: "est supérieur ou égal à",
    less_than_or_equal: "est inférieur ou égal à",
    is_empty: "est vide",
    is_not_empty: "n'est pas vide",
  }
  return names[operator]
}
