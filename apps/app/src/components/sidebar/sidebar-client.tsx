"use client"

import React from "react"
import { usePathname } from "next/navigation"
import {
  Bot,
  CreditCard,
  DollarSign,
  FileText,
  Headset,
  LayoutDashboard,
  Medal,
  Receipt,
  Users,
  Wrench,
} from "lucide-react"

import { Link } from "@nextui-org/link"
import { User as NextUIUser } from "@nextui-org/user"
import { Prisma } from "@prisma/client"

import { Icons } from "../icons"
import { useSidebarContext } from "../layout/layout-context"

import { Sidebar } from "./sidebar.styles"
import { SidebarItem } from "./sidebar-item"
import { SidebarMenu } from "./sidebar-menu"

type User = Prisma.UserGetPayload<{
  include: { profilePicture: true }
}>

interface SidebarWrapperClientProps {
  user: User
}

export const SidebarWrapperClient = ({ user }: SidebarWrapperClientProps) => {
  const pathname = usePathname().slice(3)
  const { collapsed, setCollapsed } = useSidebarContext()

  // Get user roles
  const userRoles = user?.roles || []
  // For backward compatibility, also check the legacy role field
  if (user?.role === "ADMIN" && !userRoles.includes("ADMIN")) {
    userRoles.push("ADMIN")
  }

  // Check if user has specific roles
  const isAdmin = userRoles.includes("ADMIN")
  const isSAV = isAdmin || userRoles.includes("SAV")
  const isModo = isAdmin || userRoles.includes("MODO")
  const isIABuilder = isAdmin || userRoles.includes("IA_BUILDER")

  // Nous avons toujours besoin des variables de rôle pour l'affichage conditionnel des sections du menu

  return (
    <aside className="sticky top-0 z-20 h-screen">
      {collapsed ? <div className={Sidebar.Overlay()} onClick={setCollapsed} /> : null}
      <div
        className={Sidebar({
          collapsed: collapsed,
        })}
      >
        <div className={Sidebar.Header()}>
          <div className="flex w-full items-center gap-4">
            <Link href="/">
              <span className="sr-only">Accéder au site web</span>
              <Icons.logo className="size-10 text-primary" />
            </Link>
            <span className="text-lg font-bold">Dashboard</span>
          </div>
        </div>
        <div className="flex h-full flex-col justify-between">
          <div className={Sidebar.Body()}>
            <SidebarItem title="Accueil" icon={<LayoutDashboard />} isActive={pathname === "/admin"} href="/admin" />

            {/* IA Builder Section */}
            {isIABuilder && (
              <SidebarMenu title="Gestion des IA">
                <SidebarItem
                  isActive={pathname.startsWith("/admin/agents")}
                  title="Agents"
                  icon={<Bot />}
                  href="/admin/agents"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/badges")}
                  title="Badges"
                  icon={<Medal />}
                  href="/admin/badges"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/skills")}
                  title="Skills"
                  icon={<Wrench />}
                  href="/admin/skills"
                />
              </SidebarMenu>
            )}

            {/* Modo Section */}
            {isModo && (
              <SidebarMenu title="Gestion des utilisateurs">
                <SidebarItem
                  isActive={pathname.startsWith("/admin/users")}
                  title="Utilisateurs"
                  icon={<Users />}
                  href="/admin/users"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/plans")}
                  title="Plans"
                  icon={<CreditCard />}
                  href="/admin/plans"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/subscriptions")}
                  title="Abonnements"
                  icon={<Receipt />}
                  href="/admin/subscriptions"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/refunds")}
                  title="Remboursements"
                  icon={<DollarSign />}
                  href="/admin/refunds"
                />
                <SidebarItem
                  isActive={pathname.startsWith("/admin/forms")}
                  title="Formulaires"
                  icon={<FileText />}
                  href="/admin/forms"
                />
              </SidebarMenu>
            )}

            {/* SAV Section */}
            {isSAV && (
              <SidebarMenu title="Support client">
                <SidebarItem
                  isActive={pathname.startsWith("/admin/support")}
                  title="Tickets support"
                  icon={<Headset />}
                  href="/admin/support"
                />
              </SidebarMenu>
            )}
          </div>
          <div className={Sidebar.Footer()}>
            <div className="flex w-full flex-col gap-2 rounded-md">
              <NextUIUser
                name={user?.name || user?.username || "Utilisateur"}
                description={user?.email || "Aucun email"}
                avatarProps={{
                  src: user?.image || undefined,
                  showFallback: !user?.image,
                  onLoad: (e) => e.currentTarget.querySelector("img")?.setAttribute("data-loaded", "true"),
                  fallback: (
                    <div className="flex size-6 items-center justify-center rounded-full bg-primary text-white">
                      {(user?.name || "U").charAt(0).toUpperCase()}
                    </div>
                  ),
                  className: "shrink-0",
                }}
                classNames={{
                  name: "text-sm font-medium",
                  description: "text-xs text-default-500",
                  wrapper: "w-full",
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </aside>
  )
}
