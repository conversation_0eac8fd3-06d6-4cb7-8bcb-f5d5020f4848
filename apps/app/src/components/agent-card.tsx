import React, { useState } from "react"
import Link from "next/link"
import { useSession } from "next-auth/react"
import { Heart } from "lucide-react"

import { toggleFavoriteAgent } from "@/actions/agent-actions"
import { But<PERSON> } from "@nextui-org/button"
import { Prisma } from "@prisma/client"

type AgentWithBadge = Prisma.AgentGetPayload<{
  select: {
    id: true
    icon: true
    title: true
    description: true
    badge: true
  }
}>

const AgentCard = ({ index, agent }: { index: number; agent: AgentWithBadge; isFavorite?: boolean }) => {
  const session = useSession()
  const user = session.data?.user

  const [isFavorite, setIsFavorite] = useState(Bo<PERSON>an(user?.favoriteAgentIds.includes(agent.id)))

  const toggleFavorite = async () => {
    setIsFavorite((prev) => !prev)
    const newIsFavorite = !isFavorite

    const success = await toggleFavoriteAgent(agent.id, newIsFavorite)
    if (!success) {
      setIsFavorite(!newIsFavorite)
      return
    }

    const updatedFavorites = new Set(user?.favoriteAgentIds)
    if (newIsFavorite) {
      updatedFavorites.add(agent.id)
    } else {
      updatedFavorites.delete(agent.id)
    }

    session.update({
      ...session.data,
      user: { ...session.data?.user, favoriteAgentIds: Array.from(updatedFavorites) },
    })
  }

  return (
    <div
      key={index}
      className="relative flex flex-col rounded-xl border border-default/20 bg-background p-5 shadow-md backdrop-blur-sm transition hover:shadow-lg"
    >
      <div className="grow">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2 text-2xl">{agent.icon}</div>
          {Boolean(user) && (
            <Button
              isIconOnly
              variant="light"
              type="button"
              onClick={toggleFavorite}
              aria-label="Ajouter aux favoris"
              className="z-[3]"
            >
              <Heart className={`size-6 stroke-foreground-500 ${isFavorite ? "fill-danger" : ""}`} />
            </Button>
          )}
        </div>
        <div>
          <h3 className="mt-2 line-clamp-3 text-lg font-semibold">{agent.title}</h3>
          <p className="mt-2 line-clamp-4 text-sm text-default">{agent.description}</p>
        </div>
      </div>
      <span className="mt-4 inline-block w-fit rounded-full bg-secondary-100 px-3 py-1 text-xs font-semibold text-secondary">
        {agent.badge?.title}
      </span>
      <Link href={`/agent/${agent.id}`} className="absolute inset-0 z-[2]">
        <span className="sr-only">Chat with agent</span>
      </Link>
    </div>
  )
}

export default AgentCard
