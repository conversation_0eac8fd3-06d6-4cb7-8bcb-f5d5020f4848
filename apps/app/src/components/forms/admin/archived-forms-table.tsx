"use client"

import React, { useEffect, useState } from "react"
import { Archive, BarChart3, Eye, RotateCcw, Trash2 } from "lucide-react"
import { toast } from "react-toastify"

import { useConfirmationDialog } from "@/components/ui/confirmation-dialog"
import { trpc } from "@/lib/trpc/client"
import type { RouterOutputs } from "@/lib/trpc/utils"
import { Button } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Table, TableBody, TableCell, TableColumn, TableHeader, TableRow } from "@nextui-org/table"
import { Tooltip } from "@nextui-org/tooltip"

import { FormPreviewModal } from "./form-preview-modal"

// Define proper types for form data
type FormData = RouterOutputs["form"]["getArchived"]["data"][0]

interface ArchivedFormsTableProps {
  initialPagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
  reload: boolean
  setReload: React.Dispatch<React.SetStateAction<boolean>>
  onUnarchive: () => void
}

export const ArchivedFormsTable: React.FC<ArchivedFormsTableProps> = ({
  initialPagination,
  reload,
  setReload,
  onUnarchive,
}) => {
  const [page] = useState(1)
  const [pageSize] = useState(15)
  const [selectedForm, setSelectedForm] = useState<FormData | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)

  const utils = trpc.useUtils()

  const { showConfirmation, ConfirmationDialog } = useConfirmationDialog()

  // Use tRPC query instead of local state for forms data
  const { data: formsData, refetch } = trpc.form.getArchived.useQuery({ page, pageSize })

  const forms = formsData?.data || []
  const pagination = formsData?.pagination || initialPagination

  // Handle reload trigger
  useEffect(() => {
    if (reload) {
      refetch()
      setReload(false)
    }
  }, [reload, setReload, refetch])

  const deleteForm = trpc.form.delete.useMutation({
    onSuccess: () => {
      toast.success("Formulaire supprimé avec succès")
      // Invalidate queries to trigger automatic refetch
      utils.form.getArchived.invalidate()
      utils.form.countByStatus.invalidate()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la suppression")
    },
  })

  const unarchiveForm = trpc.form.unarchive.useMutation({
    onSuccess: () => {
      toast.success("Formulaire désarchivé avec succès")
      // Invalidate all form-related queries to trigger automatic refetch
      utils.form.getAll.invalidate()
      utils.form.getArchived.invalidate()
      utils.form.countByStatus.invalidate()
      onUnarchive()
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors du désarchivage")
    },
  })

  const handleDelete = (form: FormData) => {
    const hasSubmissions = form._count.submissions > 0

    if (hasSubmissions) {
      showConfirmation({
        title: "Supprimer le formulaire avec réponses",
        message: `Ce formulaire contient ${form._count.submissions} réponse(s). Supprimer le formulaire supprimera également toutes les réponses associées. Cette action est irréversible.`,
        confirmText: "Supprimer tout",
        cancelText: "Annuler",
        variant: "danger",
        onConfirm: () => deleteForm.mutate({ id: form.id, force: true }),
      })
    } else {
      showConfirmation({
        title: "Supprimer le formulaire",
        message: "Êtes-vous sûr de vouloir supprimer ce formulaire ? Cette action est irréversible.",
        confirmText: "Supprimer",
        cancelText: "Annuler",
        variant: "danger",
        onConfirm: () => deleteForm.mutate({ id: form.id, force: false }),
      })
    }
  }

  const handleUnarchive = (formId: string) => {
    showConfirmation({
      title: "Désarchiver le formulaire",
      message: "Êtes-vous sûr de vouloir désarchiver ce formulaire ? Il redeviendra visible dans la liste principale.",
      confirmText: "Désarchiver",
      cancelText: "Annuler",
      variant: "info",
      onConfirm: () => unarchiveForm.mutate(formId),
    })
  }

  const handlePreview = (form: FormData) => {
    setSelectedForm(form)
    setIsPreviewOpen(true)
  }

  return (
    <>
      <Card>
        <CardHeader className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Formulaires archivés</h3>
            <p className="text-sm text-default-500">
              {pagination.total} formulaire{pagination.total > 1 ? "s" : ""} archivé{pagination.total > 1 ? "s" : ""}
            </p>
          </div>
        </CardHeader>
        <CardBody>
          {forms.length === 0 ? (
            <div className="py-8 text-center">
              <Archive className="mx-auto mb-4 size-12 text-default-300" />
              <p className="text-default-500">Aucun formulaire archivé</p>
              <p className="mt-2 text-sm text-default-400">Les formulaires archivés apparaîtront ici</p>
            </div>
          ) : (
            <Table aria-label="Formulaires archivés">
              <TableHeader>
                <TableColumn>TITRE</TableColumn>
                {/* <TableColumn>TYPE</TableColumn> */}
                <TableColumn>QUESTIONS</TableColumn>
                <TableColumn>SOUMISSIONS</TableColumn>
                <TableColumn>CRÉÉ PAR</TableColumn>
                <TableColumn>ACTIONS</TableColumn>
              </TableHeader>
              <TableBody>
                {forms.map((form) => (
                  <TableRow key={form.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{form.title}</p>
                        {form.description && (
                          <p className="line-clamp-2 text-sm text-default-500">{form.description}</p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{form.questions.length}</p>
                    </TableCell>
                    <TableCell>
                      <p className="text-sm">{form._count.submissions}</p>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm font-medium">{form.creator.name || "Nom non défini"}</p>
                        <p className="text-xs text-default-500">{form.creator.email}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Tooltip content="Aperçu">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="default"
                            onPress={() => handlePreview(form)}
                          >
                            <Eye className="size-4" />
                          </Button>
                        </Tooltip>

                        {form._count.submissions > 0 && (
                          <Tooltip content="Analytics">
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              color="secondary"
                              as="a"
                              href={`/admin/forms/${form.id}/analytics`}
                            >
                              <BarChart3 className="size-4" />
                            </Button>
                          </Tooltip>
                        )}

                        <Tooltip content="Désarchiver">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="primary"
                            onPress={() => handleUnarchive(form.id)}
                            isLoading={unarchiveForm.isPending}
                          >
                            <RotateCcw className="size-4" />
                          </Button>
                        </Tooltip>

                        <Tooltip content="Supprimer définitivement">
                          <Button
                            isIconOnly
                            size="sm"
                            variant="light"
                            color="danger"
                            onPress={() => handleDelete(form)}
                            isLoading={deleteForm.isPending}
                          >
                            <Trash2 className="size-4" />
                          </Button>
                        </Tooltip>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardBody>
      </Card>

      {/* Form Preview Modal */}
      <FormPreviewModal isOpen={isPreviewOpen} onClose={() => setIsPreviewOpen(false)} form={selectedForm} />

      <ConfirmationDialog />
    </>
  )
}
