"use client"

import React, { useState } from "react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Divider } from "@nextui-org/divider"
import { Spinner } from "@nextui-org/spinner"
import { FormType, QuestionType } from "@prisma/client"

import { DynamicForm } from "./dynamic-form"

export const TestFormSystem: React.FC = () => {
  const [testStep, setTestStep] = useState<"create" | "test" | "complete">("create")
  const [createdFormId, setCreatedFormId] = useState<string | null>(null)

  // Create a test form
  const createTestForm = trpc.form.create.useMutation({
    onSuccess: (form) => {
      setCreatedFormId(form.id)
      setTestStep("test")
      toast.success("Formulaire de test créé avec succès!")
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la création du formulaire de test")
    },
  })

  // Activate the test form
  const activateForm = trpc.form.activate.useMutation({
    onSuccess: () => {
      toast.success("Formulaire activé!")
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de l'activation")
    },
  })

  // Clean up test form
  const deleteTestForm = trpc.form.delete.useMutation({
    onSuccess: () => {
      setTestStep("create")
      setCreatedFormId(null)
      toast.success("Formulaire de test supprimé")
    },
    onError: (error) => {
      toast.error(error.message || "Erreur lors de la suppression")
    },
  })

  const handleCreateTestForm = () => {
    const testFormData = {
      title: "Formulaire de test - Annulation d'abonnement",
      description: "Ce formulaire est utilisé pour tester le système de formulaires dynamiques.",
      type: FormType.SUBSCRIPTION_CANCELLATION,
      showProgressBar: true,
      allowSaveProgress: false,
      requireAuth: true,
      enableConditionalLogic: false,
      questions: [
        {
          title: "Quelle est la principale raison de votre annulation ?",
          description: "Sélectionnez la raison qui correspond le mieux à votre situation.",
          type: QuestionType.SINGLE_CHOICE,
          order: 0,
          isRequired: true,
          options: [
            { id: "price", label: "Prix trop élevé", value: "price" },
            { id: "features", label: "Fonctionnalités insuffisantes", value: "features" },
            { id: "support", label: "Support client insatisfaisant", value: "support" },
            { id: "competitor", label: "Trouvé une meilleure alternative", value: "competitor" },
            { id: "other", label: "Autre raison", value: "other" },
          ],
        },
        {
          title: "Évaluez votre satisfaction globale",
          description: "Sur une échelle de 1 à 5, comment évaluez-vous votre expérience ?",
          type: QuestionType.RATING,
          order: 1,
          isRequired: true,
          minValue: 1,
          maxValue: 5,
        },
        {
          title: "Recommanderiez-vous notre service à un ami ?",
          type: QuestionType.YES_NO,
          order: 2,
          isRequired: true,
        },
        {
          title: "Commentaires additionnels",
          description: "Partagez vos commentaires pour nous aider à améliorer notre service.",
          type: QuestionType.TEXT_LONG,
          order: 3,
          isRequired: false,
          maxLength: 500,
        },
      ],
    }

    createTestForm.mutate(testFormData)
  }

  const handleActivateForm = () => {
    if (createdFormId) {
      activateForm.mutate(createdFormId)
    }
  }

  const handleFormSubmissionComplete = (submissionId: string) => {
    setTestStep("complete")
    toast.success(`Formulaire soumis avec succès! ID: ${submissionId}`)
  }

  const handleCleanup = () => {
    if (createdFormId) {
      deleteTestForm.mutate({ id: createdFormId, force: true })
    }
  }

  return (
    <div className="mx-auto max-w-4xl space-y-6">
      <Card>
        <CardHeader>
          <div>
            <h2 className="text-xl font-bold">Test du système de formulaires</h2>
            <p className="text-default-500">Testez la création, l&apos;activation et la soumission de formulaires</p>
          </div>
        </CardHeader>
        <CardBody className="space-y-4">
          {/* Step 1: Create Test Form */}
          {testStep === "create" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Étape 1: Créer un formulaire de test</h3>
              <p className="text-default-600">
                Cliquez sur le bouton ci-dessous pour créer un formulaire de test avec des questions prédéfinies.
              </p>
              <Button
                color="primary"
                onPress={handleCreateTestForm}
                isLoading={createTestForm.isPending}
                isDisabled={createTestForm.isPending}
              >
                {createTestForm.isPending ? "Création en cours..." : "Créer le formulaire de test"}
              </Button>
            </div>
          )}

          {/* Step 2: Test Form */}
          {testStep === "test" && createdFormId && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Étape 2: Tester le formulaire</h3>
                <div className="flex gap-2">
                  <Button size="sm" variant="light" onPress={handleActivateForm} isLoading={activateForm.isPending}>
                    Activer le formulaire
                  </Button>
                  <Button
                    size="sm"
                    color="danger"
                    variant="light"
                    onPress={handleCleanup}
                    isLoading={deleteTestForm.isPending}
                  >
                    Supprimer
                  </Button>
                </div>
              </div>

              <Divider />

              <DynamicForm
                formType={FormType.SUBSCRIPTION_CANCELLATION}
                onSubmissionComplete={handleFormSubmissionComplete}
                onCancel={() => setTestStep("create")}
              />
            </div>
          )}

          {/* Step 3: Complete */}
          {testStep === "complete" && (
            <div className="space-y-4 text-center">
              <div className="text-success">
                <h3 className="text-lg font-semibold text-success">✅ Test réussi!</h3>
                <p className="text-default-600">
                  Le formulaire a été soumis avec succès. Le système de formulaires fonctionne correctement.
                </p>
              </div>

              <div className="flex justify-center gap-4">
                <Button variant="light" onPress={() => setTestStep("create")}>
                  Recommencer le test
                </Button>
                <Button color="danger" variant="light" onPress={handleCleanup} isLoading={deleteTestForm.isPending}>
                  Nettoyer et terminer
                </Button>
              </div>
            </div>
          )}

          {/* Loading State */}
          {(createTestForm.isPending || deleteTestForm.isPending) && (
            <div className="flex items-center justify-center p-4">
              <Spinner size="lg" />
              <p className="ml-4">
                {createTestForm.isPending && "Création du formulaire..."}
                {deleteTestForm.isPending && "Suppression du formulaire..."}
              </p>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Test Results Summary */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Fonctionnalités testées</h3>
        </CardHeader>
        <CardBody>
          <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">Backend (tRPC)</h4>
              <ul className="space-y-1 text-default-600">
                <li>✅ Création de formulaires</li>
                <li>✅ Gestion des questions</li>
                <li>✅ Activation/désactivation</li>
                <li>✅ Soumission de réponses</li>
                <li>✅ Validation des données</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Frontend (React)</h4>
              <ul className="space-y-1 text-default-600">
                <li>✅ Rendu dynamique des questions</li>
                <li>✅ Validation côté client</li>
                <li>✅ Animations fluides</li>
                <li>✅ Barre de progression</li>
                <li>✅ Interface responsive</li>
              </ul>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}
