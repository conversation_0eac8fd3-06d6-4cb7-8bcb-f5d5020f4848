"use client"

import { useState } from "react"
import Link from "next/link"
import { DollarSign, Menu, Shield, X } from "lucide-react"

import { Locale } from "@/lib/i18n-config"
import { getDictionary } from "@/lib/langs"
import { hasRole } from "@/lib/utils/user-utils"
import { Avatar } from "@nextui-org/avatar"
import { Button } from "@nextui-org/button"
import { User, UserRole } from "@prisma/client"

import SignoutButton from "./auth/sign-out-button"
import { ThemeSwitch } from "./theme/theme-switch"
import LocaleSwitcher from "./locale-switcher"

type DictionaryType = Awaited<ReturnType<typeof getDictionary>>

export default function MobileMenu({
  lang,
  user,
  dictionary,
}: {
  lang: Locale
  user: User
  dictionary: DictionaryType
}) {
  const [isOpen, setIsOpen] = useState(false)
  const handleClose = () => setIsOpen(false)

  return (
    <div className="md:hidden">
      {/* Bouton menu burger */}
      <button onClick={() => setIsOpen(!isOpen)} className="rounded-md p-2 focus:outline-none">
        {isOpen ? <X size={28} /> : <Menu size={28} />}
      </button>

      {/* Menu mobile */}
      {isOpen && (
        <div className="absolute right-4 top-16 z-50 flex w-56 flex-col gap-3 rounded-lg bg-background p-4 shadow-lg">
          <ThemeSwitch />
          <LocaleSwitcher lang={lang} />
          {hasRole(user?.roles, [UserRole.ADMIN, UserRole.IA_BUILDER, UserRole.MODO, UserRole.SAV]) ? (
            <Link href={"/admin"} className="flex w-full items-center gap-2" onClick={handleClose}>
              <Shield className="size-7 text-default-500" />
              <span>Espace admin</span>
            </Link>
          ) : (
            <Link href={"/support"} className="flex w-full items-center gap-2" onClick={handleClose}>
              <Shield className="size-7 text-default-500" />
              <span>Centre d&apos;aide</span>
            </Link>
          )}
          <Link href={"/pricing"} className="flex w-full items-center gap-2" onClick={handleClose}>
            <DollarSign className="size-7 text-default-500" />
            <span>Tarification</span>
          </Link>
          {user ? (
            <>
              <Link href="/profile" className="flex items-center gap-2" onClick={handleClose}>
                <Avatar src={user.image ?? undefined} name={user.username ?? undefined} />
                <span>{user.username ?? "Votre compte"}</span>
              </Link>
              <SignoutButton>{dictionary.signOut}</SignoutButton>
            </>
          ) : (
            <Button as={Link} href="/sign-in" variant="ghost" className="w-full" type="button" onPress={handleClose}>
              {dictionary.signIn}
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
