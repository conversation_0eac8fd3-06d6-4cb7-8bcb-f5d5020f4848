import React from "react"

import { RefundsTable } from "@/components/refunds/admin/refunds-table"
import { serverTrpc } from "@/lib/trpc/server"

export default async function RefundsPage() {
  const { data: refunds, pagination } = await serverTrpc.refund.getAllForAdmin({
    page: 1,
    pageSize: 15,
  })

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Remboursements</h2>
      <p className="mb-2 text-gray-400">
        Gérez l&apos;ensemble des remboursements de votre plateforme depuis un seul endroit!
      </p>
      <RefundsTable initialRefunds={refunds} initialPagination={pagination} />
    </>
  )
}
