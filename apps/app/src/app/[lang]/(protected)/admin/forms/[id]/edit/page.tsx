import React from "react"
import { notFound } from "next/navigation"

import { FormEditor } from "@/components/forms/admin/form-editor"
import { serverTrpc } from "@/lib/trpc/server"

interface FormEditPageProps {
  params: {
    id: string
  }
}

export default async function FormEditPage({ params }: FormEditPageProps) {
  try {
    // Fetch the form data
    const form = await serverTrpc.form.getById(params.id)

    if (!form) {
      notFound()
    }

    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Modifier le formulaire</h2>
          <p className="text-default-500">Modifiez les questions et la configuration de votre formulaire.</p>
        </div>

        <FormEditor formData={form} />
      </div>
    )
  } catch (error) {
    notFound()
  }
}
