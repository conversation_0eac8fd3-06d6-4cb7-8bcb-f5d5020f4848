import React from "react"

import { FormsManagement } from "@/components/forms/admin/forms-management"
import { serverTrpc } from "@/lib/trpc/server"

export default async function FormsPage() {
  const [activeForms, archivedForms] = await Promise.all([
    serverTrpc.form.getAll({
      page: 1,
      pageSize: 15,
    }),
    serverTrpc.form.getArchived({
      page: 1,
      pageSize: 15,
    }),
  ])

  return (
    <>
      <h2 className="mb-2 text-3xl font-bold">Gestion des formulaires</h2>
      <p className="mb-6 text-gray-400">
        <PERSON><PERSON>ez et gérez les formulaires dynamiques pour collecter des informations des utilisateurs.
      </p>
      <FormsManagement
        initialActivePagination={activeForms.pagination}
        initialArchivedPagination={archivedForms.pagination}
      />
    </>
  )
}
