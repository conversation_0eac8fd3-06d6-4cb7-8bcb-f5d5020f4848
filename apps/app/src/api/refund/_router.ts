import { z } from "zod"

import { refundPayin } from "@/lib/mangopay"
import { prisma } from "@/lib/prisma"
import { authenticatedProcedure, modoAuthenticatedProcedure, router } from "@/lib/server/trpc"
import { logger } from "@coheadcoaching/lib"
import { RefundStatus } from "@prisma/client"
import { TRPCError } from "@trpc/server"

const createRefundSchema = z.object({
  userId: z.string(),
  planId: z.number(),
  subscriptionId: z.string().optional(),
  amount: z.number().positive(),
  mangopayPayinId: z.string().optional(),
})

const updateRefundStatusSchema = z.object({
  id: z.string(),
  status: z.enum(["PENDING", "COMPLETED", "FAILED"]),
  failureReason: z.string().optional(),
  mangopayRefundId: z.string().optional(),
})

export const refundRouter = router({
  // Create a new refund record
  create: modoAuthenticatedProcedure.input(createRefundSchema).mutation(async ({ input }) => {
    const { userId, planId, subscriptionId, amount, mangopayPayinId } = input

    // Verify the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    })

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Utilisateur non trouvé",
      })
    }

    // Verify the plan exists
    const plan = await prisma.plan.findUnique({
      where: { id: planId },
    })

    if (!plan) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Plan non trouvé",
      })
    }

    // Get the subscription to determine billing period if provided
    let subscription = null
    if (subscriptionId) {
      subscription = await prisma.subscription.findUnique({
        where: { id: subscriptionId },
      })
    }

    // Determine the appropriate refund percentage based on billing period
    const appropriateRefundPercentage =
      subscription?.billingPeriod === "MONTHLY" ? plan.monthlyRefundPercentage : plan.annualRefundPercentage

    // If no refund percentage is set, don't allow refunds
    if (!appropriateRefundPercentage || appropriateRefundPercentage <= 0) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Aucun remboursement n'est configuré pour ce plan et cette période de facturation",
      })
    }

    // Create the refund record
    const refund = await prisma.refund.create({
      data: {
        userId,
        planId,
        subscriptionId,
        amount,
        status: RefundStatus.PENDING,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        plan: {
          select: {
            id: true,
            name: true,
          },
        },
        subscription: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    })

    // If we have a MangoPay payin ID, attempt to process the refund immediately
    if (mangopayPayinId && user.mangopayUserId) {
      try {
        const mangopayRefund = await refundPayin(mangopayPayinId, user.mangopayUserId, amount)

        // Update the refund record with MangoPay details
        await prisma.refund.update({
          where: { id: refund.id },
          data: {
            status: RefundStatus.COMPLETED,
            mangopayRefundId: mangopayRefund.Id,
            processedAt: new Date(),
          },
        })

        logger.log("Refund processed successfully", {
          refundId: refund.id,
          mangopayRefundId: mangopayRefund.Id,
          amount,
        })
      } catch (error) {
        logger.error("Failed to process refund via MangoPay", {
          refundId: refund.id,
          error: error instanceof Error ? error.message : String(error),
        })

        // Update the refund record to reflect the failure
        await prisma.refund.update({
          where: { id: refund.id },
          data: {
            status: RefundStatus.FAILED,
            failureReason: error instanceof Error ? error.message : "Erreur inconnue",
          },
        })
      }
    }

    return refund
  }),

  // Retry failed refund
  retry: modoAuthenticatedProcedure.input(z.string()).mutation(async ({ input: refundId }) => {
    const refund = await prisma.refund.findUnique({
      where: { id: refundId },
      include: {
        subscription: {
          include: {
            user: true,
            payments: {
              where: { status: "SUCCEEDED" },
              orderBy: { createdAt: "desc" },
              take: 1,
            },
          },
        },
      },
    })

    if (!refund) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Refund not found",
      })
    }

    if (refund.status !== RefundStatus.FAILED) {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Only failed refunds can be retried",
      })
    }

    // Check if subscription is canceled (required for retry)
    if (refund.subscription?.status !== "CANCELED") {
      throw new TRPCError({
        code: "PRECONDITION_FAILED",
        message: "Subscription must be canceled to retry refund",
      })
    }

    // Reset refund status to pending and clear failure reason
    const updatedRefund = await prisma.refund.update({
      where: { id: refundId },
      data: {
        status: RefundStatus.PENDING,
        failureReason: null,
        processedAt: null,
      },
    })

    // TODO: Trigger refund processing logic here
    // For now, we just reset the status - the actual refund processing
    // would be handled by a background job or webhook

    return {
      success: true,
      message: "Refund retry initiated",
      refund: updatedRefund,
    }
  }),

  // Update refund status (admin only)
  updateStatus: modoAuthenticatedProcedure.input(updateRefundStatusSchema).mutation(async ({ input }) => {
    const { id, status, failureReason, mangopayRefundId } = input

    const refund = await prisma.refund.findUnique({
      where: { id },
    })

    if (!refund) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Remboursement non trouvé",
      })
    }

    const updateData: {
      status: RefundStatus
      processedAt?: Date
      mangopayRefundId?: string
      failureReason?: string
    } = {
      status: status as RefundStatus,
    }

    if (status === "COMPLETED") {
      updateData.processedAt = new Date()
      if (mangopayRefundId) {
        updateData.mangopayRefundId = mangopayRefundId
      }
    }

    if (status === "FAILED" && failureReason) {
      updateData.failureReason = failureReason
    }

    return await prisma.refund.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        plan: {
          select: {
            id: true,
            name: true,
          },
        },
        subscription: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    })
  }),

  // Get all refunds for admin
  getAllForAdmin: modoAuthenticatedProcedure
    .input(
      z.object({
        page: z.number().min(1).default(1),
        pageSize: z.number().min(1).max(50).default(15),
        status: z.enum(["PENDING", "COMPLETED", "FAILED"]).optional(),
      })
    )
    .query(async ({ input }) => {
      const { page, pageSize, status } = input
      const skip = (page - 1) * pageSize

      const where = status ? { status: status as RefundStatus } : {}

      const refunds = await prisma.refund.findMany({
        where,
        skip,
        take: pageSize,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          plan: {
            select: {
              id: true,
              name: true,
            },
          },
          subscription: {
            select: {
              id: true,
              status: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      })

      const totalCount = await prisma.refund.count({ where })

      return {
        data: refunds,
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages: Math.ceil(totalCount / pageSize),
        },
      }
    }),

  // Get user's refunds
  getMyRefunds: authenticatedProcedure.query(async ({ ctx }) => {
    if (!ctx.session?.user?.id) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Non authentifié",
      })
    }

    return await prisma.refund.findMany({
      where: {
        userId: ctx.session.user.id,
      },
      include: {
        plan: {
          select: {
            id: true,
            name: true,
          },
        },
        subscription: {
          select: {
            id: true,
            status: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    })
  }),

  // Get refund by ID
  getById: modoAuthenticatedProcedure.input(z.string()).query(async ({ input }) => {
    const refund = await prisma.refund.findUnique({
      where: { id: input },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        plan: {
          select: {
            id: true,
            name: true,
          },
        },
        subscription: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    })

    if (!refund) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Remboursement non trouvé",
      })
    }

    return refund
  }),
})
