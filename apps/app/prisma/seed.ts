import { config } from "dotenv"
config()
import ora from "ora"

import { rolesAsObject } from "@/constants"
import { hash } from "@/lib/bcrypt"
import { env } from "@/lib/env"
import { handleMangopayResponse } from "@/lib/mangopay"
import { MangopayUser, MangopayWallet } from "@/types/mangopay"
import { logger, startTask } from "@coheadcoaching/lib"
import { PrismaClient } from "@prisma/client"

const spinner = ora()

const prisma = new PrismaClient()

async function main() {
  try {
    //* Admin
    let adminExists = await prisma.user.findFirst({
      where: {
        email: env.AUTH_ADMIN_EMAIL,
      },
    })
    if (!adminExists) {
      const task = await startTask({
        name: "Creating admin",
        successMessage: "Admin created",
      })
      adminExists = await prisma.user.create({
        data: {
          email: env.AUTH_ADMIN_EMAIL as string,
          password: await hash(env.AUTH_ADMIN_PASSWORD ?? "", 12),
          role: rolesAsObject.admin,
          emailVerified: new Date(),
          hasPassword: true,
          name: "Admin",
        },
      })
      task.stop()
    } else {
      logger.log("Admin already exists")
    }

    //* Seed Plans
    const plansExist = await prisma.plan.findFirst()
    if (!plansExist) {
      const task = await startTask({
        name: "Creating plans",
        successMessage: "Plans created successfully!",
      })
      await prisma.plan.createMany({
        data: [
          {
            name: "Basic",
            description: "Plan basique pour démarrer",
            monthlyPrice: 999, // 9.99€
            annualPrice: 9990, // 99.90€
            features: [
              JSON.stringify({ text: "5 Agents", included: true }),
              JSON.stringify({ text: "100 Messages/mois", included: true }),
              JSON.stringify({ text: "Modèles standards", included: true }),
              JSON.stringify({ text: "Support par email", included: true }),
              JSON.stringify({ text: "Analyses avancées", included: false }),
            ],
            isRecommended: false,
            isActive: true,
          },
          {
            name: "Standard",
            description: "Plan standard avec plus de fonctionnalités",
            monthlyPrice: 2499, // 24.99€
            annualPrice: 24990, // 249.90€
            features: [
              JSON.stringify({ text: "15 Agents", included: true }),
              JSON.stringify({ text: "500 Messages/mois", included: true }),
              JSON.stringify({ text: "Modèles avancés", included: true }),
              JSON.stringify({ text: "Support prioritaire", included: true }),
              JSON.stringify({ text: "Analyses avancées", included: true }),
            ],
            isRecommended: true,
            isActive: true,
          },
          {
            name: "Premium",
            description: "Plan premium avec toutes les fonctionnalités",
            monthlyPrice: 4999, // 49.99€
            annualPrice: 49990, // 499.90€
            features: [
              JSON.stringify({ text: "Agents illimités", included: true }),
              JSON.stringify({ text: "Messages illimités", included: true }),
              JSON.stringify({ text: "Tous les modèles", included: true }),
              JSON.stringify({ text: "Support 24/7", included: true }),
              JSON.stringify({ text: "Analyses personnalisées", included: true }),
            ],
            isRecommended: false,
            isActive: true,
          },
        ],
        skipDuplicates: true,
      })
      task.stop()
    } else {
      logger.log("Plans already exists!")
    }

    // Check if admin exists and doesn't have mangopay wallet
    if (adminExists && !adminExists.mangopayWalletId) {
      const task = await startTask({
        name: "Creating Mangopay account for admin",
        successMessage: "Mangopay account created successfully",
      })

      try {
        const myHeaders = new Headers()
        myHeaders.append("Content-Type", "application/json")
        myHeaders.append("Authorization", `Bearer ${env.MANGOPAY_PROXY_AUTH_KEY}`)

        const userData = JSON.stringify({
          Address: {
            AddressLine1: "2795 Edgewood Road",
            AddressLine2: null,
            City: "Little Rock",
            Region: "Arkansas",
            PostalCode: "72212",
            Country: "US",
          },
          FirstName: "John",
          LastName: "Doe",
          Birthday: *********,
          Nationality: "FR",
          CountryOfResidence: "US",
          Tag: "The first user",
          Email: env.AUTH_ADMIN_EMAIL,
          TermsAndConditionsAccepted: true,
          UserCategory: "OWNER",
          PersonType: "NATURAL",
        })

        const createMangoUserOptions = {
          method: "POST",
          headers: myHeaders,
          body: userData,
        }

        const response = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/users`, createMangoUserOptions)

        if (!response.ok) {
          throw new Error(`Failed to create Mangopay user: ${response.status} ${response.statusText}`)
        }

        const mangopayUser = handleMangopayResponse<MangopayUser>(await response.json())
        logger.log(mangopayUser)

        await prisma.user.update({
          where: {
            email: env.AUTH_ADMIN_EMAIL,
          },
          data: {
            mangopayUserId: mangopayUser.Id,
          },
        })

        const walletData = JSON.stringify({
          Owners: [mangopayUser.Id],
          Currency: "EUR",
          Description: "User wallet",
          Tag: "Created using Mangopay NodeJS SDK",
        })

        const createWalletOptions = {
          method: "POST",
          headers: myHeaders,
          body: walletData,
        }

        const walletResponse = await fetch(`${env.MANGOPAY_PROXY_URL}/api/v1/mangopay/wallets`, createWalletOptions)

        if (!walletResponse.ok) {
          throw new Error(`Failed to create Mangopay wallet: ${walletResponse.status} ${walletResponse.statusText}`)
        }

        const mangopayWallet = handleMangopayResponse<MangopayWallet>(await walletResponse.json())
        logger.log(mangopayWallet)

        await prisma.user.update({
          where: {
            email: env.AUTH_ADMIN_EMAIL,
          },
          data: {
            mangopayWalletId: mangopayWallet.Id,
          },
        })

        task.stop()
      } catch (error) {
        task.stop()
        logger.error(`Error creating Mangopay account: ${error instanceof Error ? error.message : String(error)}`)
      }
    } else if (adminExists?.mangopayWalletId) {
      logger.log("Admin already has Mangopay wallet")
    }

    //* Seed Skills
    if (await prisma.skill.findFirst()) {
      logger.log("Skills already exists")
    } else {
      await prisma.skill.createMany({
        data: [
          { name: "Writing" },
          { name: "Content Creation" },
          { name: "SEO" },
          { name: "Analytics" },
          { name: "Research" },
          { name: "Copywriting" },
          { name: "Programming" },
          { name: "Data Analysis" },
          { name: "Design" },
          { name: "Marketing" },
          { name: "Project Management" },
          { name: "Education" },
        ],
        skipDuplicates: true,
      })

      logger.log("Skills seeded successfully.")

      //* Seed Badges
      await prisma.badge.createMany({
        data: [
          { title: "Expert AI" },
          { title: "Data Analyst" },
          { title: "Full Stack Developer" },
          { title: "Math Teacher" },
          { title: "Content Creator" },
          { title: "SEO Specialist" },
          { title: "Copywriter" },
          { title: "Marketing Expert" },
        ],
        skipDuplicates: true,
      })

      logger.log("Badges seeded successfully.")

      //* Seed Categories
      await prisma.category.createMany({
        data: [
          { name: "Technology" },
          { name: "Science" },
          { name: "Education" },
          { name: "Copywriting" },
          { name: "Content Creation" },
          { name: "SEO" },
          { name: "Marketing" },
          { name: "Création de titres" },
        ],
        skipDuplicates: true,
      })

      logger.log("Categories seeded successfully.")

      //* Fetch badges
      const expertAiBadge = await prisma.badge.findFirst({
        where: { title: "Expert AI" },
      })

      const contentCreatorBadge = await prisma.badge.findFirst({
        where: { title: "Content Creator" },
      })

      const copywriterBadge = await prisma.badge.findFirst({
        where: { title: "Copywriter" },
      })

      const seoSpecialistBadge = await prisma.badge.findFirst({
        where: { title: "SEO Specialist" },
      })

      if (!expertAiBadge || !contentCreatorBadge || !copywriterBadge || !seoSpecialistBadge) {
        throw new Error("Missing required Badges for seeding.")
      }

      //* Seed Agents
      await prisma.agent.createMany({
        data: [
          { icon: "🤖", title: "AI Assistant", description: "Helps with AI queries", badgeId: expertAiBadge.id },
          { icon: "📊", title: "Data Guru", description: "Analyzes data efficiently", badgeId: expertAiBadge.id },
          { icon: "💻", title: "Code Expert", description: "Provides programming guidance", badgeId: expertAiBadge.id },
          {
            icon: "🔍",
            title: "Research Specialist",
            description: "Conducts thorough research",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "📝",
            title: "Content Writer",
            description: "Creates engaging content",
            badgeId: contentCreatorBadge.id,
          },
          { icon: "🎨", title: "Design Assistant", description: "Helps with visual design", badgeId: expertAiBadge.id },
          {
            icon: "📈",
            title: "Marketing Strategist",
            description: "Develops marketing plans",
            badgeId: expertAiBadge.id,
          },
          { icon: "🔐", title: "Security Advisor", description: "Ensures system security", badgeId: expertAiBadge.id },
          { icon: "🌐", title: "Web Developer", description: "Builds web applications", badgeId: expertAiBadge.id },
          {
            icon: "📱",
            title: "Mobile Expert",
            description: "Specializes in mobile development",
            badgeId: expertAiBadge.id,
          },
          { icon: "🧪", title: "QA Tester", description: "Ensures quality assurance", badgeId: expertAiBadge.id },
          { icon: "📚", title: "Education Guide", description: "Assists with learning", badgeId: expertAiBadge.id },
          {
            icon: "💡",
            title: "Innovation Coach",
            description: "Sparks creative solutions",
            badgeId: expertAiBadge.id,
          },
          { icon: "🗣️", title: "Language Tutor", description: "Teaches multiple languages", badgeId: expertAiBadge.id },
          {
            icon: "📋",
            title: "Project Manager",
            description: "Coordinates project execution",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "🎯",
            title: "Strategy Consultant",
            description: "Provides strategic guidance",
            badgeId: expertAiBadge.id,
          },
          { icon: "🤝", title: "HR Assistant", description: "Helps with HR matters", badgeId: expertAiBadge.id },
          {
            icon: "📢",
            title: "Communications Expert",
            description: "Improves communication",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "⚙️",
            title: "Systems Engineer",
            description: "Manages system architecture",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "📦",
            title: "Product Manager",
            description: "Oversees product development",
            badgeId: expertAiBadge.id,
          },
          { icon: "🎮", title: "Game Developer", description: "Creates gaming experiences", badgeId: expertAiBadge.id },
          {
            icon: "🔧",
            title: "DevOps Engineer",
            description: "Manages deployment pipeline",
            badgeId: expertAiBadge.id,
          },
          { icon: "📱", title: "UX Designer", description: "Improves user experience", badgeId: expertAiBadge.id },
          { icon: "🎥", title: "Video Editor", description: "Creates video content", badgeId: contentCreatorBadge.id },
          {
            icon: "🎵",
            title: "Audio Specialist",
            description: "Handles audio production",
            badgeId: contentCreatorBadge.id,
          },
          {
            icon: "📸",
            title: "Photography Expert",
            description: "Guides photo techniques",
            badgeId: contentCreatorBadge.id,
          },
          { icon: "🤔", title: "Problem Solver", description: "Resolves complex issues", badgeId: expertAiBadge.id },
          { icon: "📰", title: "News Analyst", description: "Analyzes current events", badgeId: expertAiBadge.id },
          {
            icon: "🌍",
            title: "Cultural Advisor",
            description: "Provides cultural insights",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "🔬",
            title: "Science Expert",
            description: "Explains scientific concepts",
            badgeId: expertAiBadge.id,
          },
          {
            icon: "✍️",
            title: "Blog Writer",
            description: "Creates engaging blog content",
            badgeId: copywriterBadge.id,
          },
          {
            icon: "📈",
            title: "SEO Expert",
            description: "Optimizes content for search engines",
            badgeId: seoSpecialistBadge.id,
          },
          {
            icon: "📋",
            title: "Content Strategist",
            description: "Plans and organizes content creation",
            badgeId: contentCreatorBadge.id,
          },
          {
            icon: "🏷️",
            title: "Title Creator",
            description: "Crafts engaging and SEO-friendly titles",
            badgeId: seoSpecialistBadge.id,
          },
        ],
        skipDuplicates: true,
      })

      logger.log("Agents seeded successfully.")

      //* Connect Skills to Agents
      // Get all skills
      const skills = await prisma.skill.findMany()

      // Get specific agents to connect with skills
      const contentWriter = await prisma.agent.findFirst({ where: { title: "Content Writer" } })
      const blogWriter = await prisma.agent.findFirst({ where: { title: "Blog Writer" } })
      const seoExpert = await prisma.agent.findFirst({ where: { title: "SEO Expert" } })
      const titleCreator = await prisma.agent.findFirst({ where: { title: "Title Creator" } })

      if (contentWriter && blogWriter && seoExpert && titleCreator) {
        // Get specific skills
        const writingSkill = skills.find((s) => s.name === "Writing")
        const contentCreationSkill = skills.find((s) => s.name === "Content Creation")
        const seoSkill = skills.find((s) => s.name === "SEO")
        const copywritingSkill = skills.find((s) => s.name === "Copywriting")

        if (writingSkill && contentCreationSkill && seoSkill && copywritingSkill) {
          // Connect skills to agents
          await prisma.agent.update({
            where: { id: contentWriter.id },
            data: { skills: { connect: [{ id: writingSkill.id }, { id: contentCreationSkill.id }] } },
          })

          await prisma.agent.update({
            where: { id: blogWriter.id },
            data: {
              skills: {
                connect: [{ id: writingSkill.id }, { id: contentCreationSkill.id }, { id: copywritingSkill.id }],
              },
            },
          })

          await prisma.agent.update({
            where: { id: seoExpert.id },
            data: { skills: { connect: [{ id: seoSkill.id }, { id: contentCreationSkill.id }] } },
          })

          await prisma.agent.update({
            where: { id: titleCreator.id },
            data: { skills: { connect: [{ id: seoSkill.id }, { id: copywritingSkill.id }] } },
          })
        }
      }

      logger.log("Skills connected to agents successfully.")

      //* Fetch categories
      const techCategory = await prisma.category.findFirst({ where: { name: "Technology" } })
      const copywritingCategory = await prisma.category.findFirst({ where: { name: "Copywriting" } })
      const titlesCategory = await prisma.category.findFirst({ where: { name: "Création de titres" } })

      if (!techCategory || !copywritingCategory || !titlesCategory) {
        throw new Error("Missing required Categories for seeding.")
      }

      //* Fetch agents for prompts
      const aiAssistant = await prisma.agent.findFirst({ where: { title: "AI Assistant" } })
      const blogWriterAgent = await prisma.agent.findFirst({ where: { title: "Blog Writer" } })
      const titleCreatorAgent = await prisma.agent.findFirst({ where: { title: "Title Creator" } })

      if (!aiAssistant || !blogWriterAgent || !titleCreatorAgent) {
        throw new Error("Missing required Agents for Prompt seeding.")
      }

      //* Seed Prompts
      await prisma.prompt.createMany({
        data: [
          // Technology prompts
          {
            title: "Machine Learning Basics",
            body: "What is Machine Learning?",
            agentId: aiAssistant.id,
            categoryId: techCategory.id,
          },
          {
            title: "Neural Networks Explained",
            body: "Explain Neural Networks.",
            agentId: aiAssistant.id,
            categoryId: techCategory.id,
          },

          // Copywriting prompts
          {
            title: "Rédaction d'un article de blog analytique",
            body: `Tu es un expert en rédaction analytique spécialisé dans les analyses d'événements marquants et leur impact sur des industries ou des communautés spécifiques. Peux-tu m'aider à créer un article de blog approfondi et analytique détaillant les ramifications conséquentes et les implications futures potentielles d'un événement ou d'une nouvelle spécifique ? Pour cela, j'ai besoin que tu me demandes :

- Quel est l'événement ou la nouvelle spécifique à analyser ?
- Quelle est l'industrie ou la communauté spécifique concernée par cet événement ?
- Souhaites-tu te concentrer sur des perspectives particulières, comme économiques, sociopolitiques ou environnementales ?
- As-tu des données spécifiques, des opinions d'experts, ou des études de cas que tu aimerais voir incluses, ou dois-je les rechercher ?
- Y a-t-il des stratégies ou des adaptations spécifiques que tu envisages déjà pour atténuer les impacts négatifs, ou devrais-je proposer des suggestions générales ?

En fonction des réponses, je te rédigerai un article offrant un aperçu complet des effets immédiats et à long terme de cet événement, enrichi de données, d'opinions d'experts et d'études de cas, pour fournir une compréhension à facettes multiples.`,
            agentId: blogWriterAgent.id,
            categoryId: copywritingCategory.id,
          },
          {
            title: "Analyse et application d'un ouvrage",
            body: `**Tu es un expert en rédaction de contenu spécialisé dans l'analyse et l'application des idées issues de la littérature et des articles influents.**

**Tâche :**

Rédige un article de blog qui extrait les principaux enseignements d'un ouvrage ou article influent. L'objectif est d'expliquer les concepts clés, d'analyser leur impact et de détailler leurs applications concrètes dans un domaine spécifique.

Cet article vise à rendre accessibles des idées puissantes issues de la littérature ou d'articles spécialisés, en les appliquant à un secteur précis pour aider les lecteurs à en tirer des enseignements concrets.

**Contraintes :**

- Le ton doit être **accessible et engageant**, afin de capter l'attention du lecteur.
- L'article doit inclure **des exemples concrets** et **des recommandations pratiques** pour illustrer chaque enseignement.
- La conclusion doit résumer les points essentiels et proposer **des pistes d'action** pour une mise en pratique immédiate.

**Format de réponse attendu :**

1. **Introduction** : Présentation du livre/article et de son importance.
2. **Concepts clés** : Décryptage des idées majeures.
3. **Impact et applications concrètes** : Analyse des implications dans un domaine spécifique, avec des exemples.
4. **Conclusion** : Synthèse des enseignements et recommandations pratiques.

**Étapes de la tâche :**

1. Identifier l'ouvrage ou l'article et son auteur.
2. Extraire les concepts clés et expliquer leur signification.
3. Analyser leur impact et leur pertinence.
4. Proposer des applications concrètes dans un domaine précis.
5. Fournir des recommandations pratiques pour les lecteurs.

### Pose moi ces questions avant de passer à la rédaction

Quel est le titre et l'auteur du livre ou de l'article que tu souhaites analyser ? Dans quel domaine ou industrie souhaites-tu appliquer ses enseignements ?`,
            agentId: blogWriterAgent.id,
            categoryId: copywritingCategory.id,
          },
          {
            title: "Correction des idées fausses",
            body: `Tu es un expert en rédaction de contenu et en vulgarisation, spécialisé dans la clarification des idées reçues et des malentendus courants.

Rédige un article de blog détaillé qui explore les idées fausses courantes sur un sujet spécifique et explique comment elles peuvent être corrigées. L'objectif est d'informer le lecteur de manière claire et engageante, en s'appuyant sur des faits vérifiés et des sources fiables.

### **Contexte**

Les idées fausses se répandent facilement, que ce soit dans les domaines scientifiques, historiques, culturels ou techniques. Un bon article doit identifier ces erreurs, expliquer pourquoi elles existent et fournir une correction basée sur des faits avérés.

### **Contraintes**

- L'article doit être accessible à un large public, sans jargon excessif.
- Il doit inclure des exemples concrets pour illustrer chaque idée fausse.
- La correction de chaque idée doit être appuyée par des sources ou une explication claire.
- Un ton pédagogique et engageant est recommandé.

### **Format attendu**

L'article doit être structuré de la manière suivante :

1. **Introduction** : Présentation du sujet et de l'importance de corriger les idées fausses.
2. **Liste des idées fausses** : Chaque idée fausse est décrite, expliquée, puis corrigée avec des faits précis.
3. **Conclusion** : Synthèse et encouragement à vérifier les sources et à penser de manière critique.

### **Étapes de la tâche**

1. Identifier un sujet spécifique où plusieurs idées fausses existent.
2. Recenser les idées reçues les plus courantes sur ce sujet.
3. Expliquer pourquoi ces idées sont erronées.
4. Apporter une correction basée sur des faits ou des sources fiables.
5. Structurer le tout sous forme d'un article clair et engageant.

### **Objectif final**

Fournir un contenu éducatif et impactant qui aide les lecteurs à mieux comprendre un sujet en déconstruisant les idées reçues et en les remplaçant par des informations correctes.

**Poses moi cette question avant de passer à la rédaction**

**Quel est le sujet spécifique sur lequel tu souhaites déconstruire des idées fausses ?**`,
            agentId: blogWriterAgent.id,
            categoryId: copywritingCategory.id,
          },
          {
            title: "Article explicatif sur un processus",
            body: `Tu es un expert en rédaction et en vulgarisation de concepts complexes, spécialisé dans la création d'articles informatifs et pédagogiques.

Rédige un article détaillé expliquant le processus de [tâche ou projet spécifique]. L'objectif est d'aider le lecteur à comprendre les étapes essentielles et de lui fournir des conseils pratiques pour optimiser la réalisation de cette tâche.

Cet article vise à offrir une explication claire et structurée, adaptée à un public qui souhaite maîtriser ce processus, qu'il soit débutant ou intermédiaire.

### Contraintes :

- Le ton doit être clair, accessible et pédagogique.
- L'article doit inclure des exemples concrets ou des études de cas si pertinent.
- Les conseils donnés doivent être actionnables et adaptés aux défis courants rencontrés dans ce processus.

### Format attendu :

1. **Introduction** : Présentation de la tâche/projet et son importance.
2. **Explication du processus** : Étapes détaillées, illustrées si nécessaire.
3. **Conseils pratiques** : Recommandations pour améliorer l'efficacité.
4. **Erreurs courantes à éviter** : Identification des pièges les plus fréquents.
5. **Conclusion** : Récapitulatif et encouragement à l'action.

### Étapes de la tâche :

1. Identifier la tâche ou le projet à expliquer.
2. Détailler chaque étape du processus de manière logique et progressive.
3. Ajouter des conseils pratiques pour optimiser la réalisation de cette tâche.
4. Illustrer avec des exemples concrets si nécessaire.
5. Mettre en avant les erreurs fréquentes à éviter.
6. Rédiger une conclusion synthétique et motivante.

### Objectif final :

Produire un article clair et structuré qui guide efficacement le lecteur dans la compréhension et la mise en pratique du processus décrit.

Poses moi cette question avant de passer à la rédaction

Quel est le processus ou le projet spécifique que vous souhaitez expliquer dans cet article ?`,
            agentId: blogWriterAgent.id,
            categoryId: copywritingCategory.id,
          },
          {
            title: "Article sur un passe-temps",
            body: `Tu es un rédacteur spécialisé en création de contenu engageant et informatif pour les blogs.

Rédige un article de blog mettant en avant les avantages d'une activité spécifique. L'article devra expliquer pourquoi cette activité est bénéfique, comment un débutant peut s'y initier, et quel équipement est nécessaire pour bien commencer.

Cet article vise à informer et à encourager les lecteurs à essayer une nouvelle activité ou passe-temps en leur fournissant des conseils pratiques et des recommandations adaptées.

**Contraintes :**

- L'article doit être clair, structuré et accessible aux débutants.
- Il doit inclure des informations vérifiées sur les bénéfices de l'activité et des recommandations pratiques pour bien débuter.
- Un ton engageant et motivant est préférable pour capter l'attention des lecteurs.

**Format attendu :**

- **Introduction** : Présentation rapide de l'activité et de son intérêt.
- **Avantages** : Pourquoi cette activité est bénéfique (bienfaits physiques, mentaux, sociaux, etc.).
- **Comment commencer** : Conseils pour les débutants, étapes essentielles à suivre.
- **Équipement nécessaire** : Liste des éléments indispensables et recommandations.
- **Conclusion** : Résumé et encouragement à essayer l'activité.

**Étapes de la tâche :**

1. Identifier l'activité ou le passe-temps concerné.
2. Rechercher et détailler ses principaux avantages.
3. Proposer des conseils pratiques pour débuter.
4. Lister l'équipement nécessaire et expliquer son utilité.
5. Structurer l'article de manière fluide et engageante.

**Objectif final :**

Fournir un article de blog informatif et attrayant qui motive les lecteurs à s'initier à une nouvelle activité tout en leur donnant les clés pour bien démarrer.

Poses moi cette question avant de passer à la rédaction
 **Quelle est l'activité ou le passe-temps spécifique sur lequel doit porter l'article ?**`,
            agentId: blogWriterAgent.id,
            categoryId: copywritingCategory.id,
          },

          // Création de titres prompts
          {
            title: "Stratégies pour titres captivants SEO",
            body: `Tu es un expert en rédaction web et en SEO, spécialisé dans l'optimisation des titres d'articles pour maximiser leur impact et leur visibilité.

Analyse et propose des directives détaillées sur les meilleures stratégies pour créer un titre d'article efficace, captivant et engageant. L'objectif est d'optimiser le titre à la fois pour l'engagement du lecteur et pour le référencement naturel (SEO).

L'article abordera un sujet spécifique, et le titre devra refléter l'intention communicative tout en prenant en compte :

- Les attentes et les intérêts du public cible
- Les tendances et mots-clés les plus pertinents
- Les déclencheurs psychologiques incitant au clic
- Le style linguistique adapté au ton et au format de l'article

### Contraintes :

- Le titre doit être à la fois attrayant pour les lecteurs et optimisé pour les moteurs de recherche
- Il doit répondre aux exigences SEO (longueur idéale, présence de mots-clés, pertinence)
- Il doit s'intégrer harmonieusement dans la ligne éditoriale du site ou de la plateforme de publication

### Format de réponse attendu :

1. **Analyse des stratégies de titres efficaces** (Clickbait, informatif, émotionnel, chiffré, interrogatif, etc.)
2. **Recommandations sur les styles linguistiques les plus adaptés** en fonction du ton et du public
3. **Identification des déclencheurs psychologiques puissants** qui encouragent le clic (curiosité, urgence, bénéfice clair, etc.)
4. **Optimisation SEO du titre** (longueur optimale, structuration, placement des mots-clés)
5. **Exemples concrets de titres optimisés pour le sujet demandé**

### Étapes de la tâche :

1. Identifier le type d'article et son intention communicative
2. Définir les attentes du public cible
3. Rechercher les mots-clés tendances liés au sujet
4. Sélectionner les stratégies linguistiques et psychologiques adaptées
5. Formuler plusieurs suggestions de titres optimisés

### Objectif final :

Obtenir une méthodologie complète pour rédiger un titre efficace et optimisé SEO, en tenant compte des attentes des lecteurs et des exigences des moteurs de recherche.

**Poses moi cette question avant de commencer**

**Quel est le sujet précis de l'article pour lequel tu souhaites optimiser le titre ?**`,
            agentId: titleCreatorAgent.id,
            categoryId: titlesCategory.id,
          },
          {
            title: "Optimisation de titre accrocheur",
            body: `Tu es un expert en copywriting et en marketing de contenu, spécialisé dans la création de titres percutants qui captent l'attention et incitent à l'action.

Ta tâche est d'élaborer des stratégies pour créer un titre efficace et engageant en fonction du type de contenu à promouvoir. L'objectif est d'optimiser l'impact du titre pour maximiser le taux de clics et l'engagement du public cible.

Un bon titre doit être clair, intrigant et pertinent pour son audience. Il doit refléter le sujet du contenu tout en suscitant la curiosité ou en mettant en avant une promesse de valeur.

### **Contraintes :**

- Le titre doit être adapté au format du contenu (article, vidéo, post social media, newsletter, etc.).
- Il doit capter l'attention rapidement et correspondre aux attentes du public.
- L'optimisation SEO peut être un critère selon la plateforme utilisée.

### **Format de réponse attendu :**

- Principes généraux d'un titre percutant.
- Suggestions de titres selon le type de contenu.
- Analyse des éléments qui rendent un titre efficace.
- Techniques pour tester et améliorer un titre (A/B testing, analyse de performance, etc.).

### **Étapes de la tâche :**

1. Identifier les caractéristiques d'un bon titre en fonction du support.
2. Analyser les attentes et comportements du public cible.
3. Proposer des exemples de titres adaptés au type de contenu.
4. Expliquer les techniques pour tester et améliorer l'impact du titre.

### **Objectif final :**

Fournir des recommandations précises et actionnables pour créer un titre accrocheur et optimisé, capable de capter l'attention et d'inciter à l'engagement.

Poses moi ces questions avant de commencer

Quel est le type de contenu pour lequel tu souhaites un titre accrocheur (article, vidéo, email marketing, post sur les réseaux sociaux, etc.) ? Quel est le sujet ?`,
            agentId: titleCreatorAgent.id,
            categoryId: titlesCategory.id,
          },
        ],
        skipDuplicates: true,
      })

      logger.log("Prompts seeded successfully.")
    }

    //* Seed Subscription Cancellation Form
    const formsExist = await prisma.form.findFirst()
    if (!formsExist) {
      const task = await startTask({
        name: "Creating subscription cancellation form",
        successMessage: "Subscription cancellation form created successfully!",
      })

      // Create the subscription cancellation form
      const cancellationForm = await prisma.form.create({
        data: {
          title: "Formulaire d'annulation d'abonnement",
          description:
            "Nous sommes désolés de vous voir partir. Vos commentaires nous aident à améliorer notre service pour tous nos utilisateurs.",
          type: "SUBSCRIPTION_CANCELLATION",
          status: "INACTIVE", // Inactive by default for admin testing
          isActive: false,
          showProgressBar: true,
          allowSaveProgress: false,
          requireAuth: true,
          enableConditionalLogic: true,
          createdBy: adminExists.id,
        },
      })

      // Define types for the seed data
      interface QuestionOption {
        id: string
        label: string
        value: string
      }

      interface ConditionalLogicRule {
        id: string
        questionId: string
        operator:
          | "equals"
          | "not_equals"
          | "contains"
          | "not_contains"
          | "greater_than"
          | "less_than"
          | "greater_than_or_equal"
          | "less_than_or_equal"
          | "is_empty"
          | "is_not_empty"
        value: string | number | boolean | string[]
      }

      interface ConditionalLogicGroup {
        id: string
        rules: ConditionalLogicRule[]
        logic: "AND" | "OR"
      }

      interface ShowConditions {
        groups: ConditionalLogicGroup[]
        groupLogic: "AND" | "OR"
      }

      interface QuestionData {
        title: string
        description?: string
        type: "SINGLE_CHOICE" | "TEXT_LONG" | "TEXT_SHORT" | "RATING" | "MULTIPLE_CHOICE" | "YES_NO" | "EMAIL" | "DATE"
        order: number
        isRequired: boolean
        minLength?: number
        maxLength?: number
        minValue?: number
        maxValue?: number
        options?: QuestionOption[]
        showConditions?: ShowConditions
      }

      // Create form questions with various types and conditional logic
      const questions: QuestionData[] = [
        {
          title: "Quelle est la principale raison de votre annulation ?",
          description: "Sélectionnez la raison qui correspond le mieux à votre situation.",
          type: "SINGLE_CHOICE",
          order: 0,
          isRequired: true,
          options: [
            { id: "price", label: "Le prix est trop élevé", value: "price" },
            { id: "features", label: "Fonctionnalités insuffisantes", value: "features" },
            { id: "usability", label: "Difficultés d'utilisation", value: "usability" },
            { id: "support", label: "Support client insatisfaisant", value: "support" },
            { id: "competitor", label: "J'ai trouvé une meilleure alternative", value: "competitor" },
            { id: "temporary", label: "Pause temporaire", value: "temporary" },
            { id: "other", label: "Autre raison", value: "other" },
          ],
        },
        {
          title: "Pouvez-vous nous expliquer davantage ?",
          description: "Décrivez brièvement les détails de votre situation (optionnel).",
          type: "TEXT_LONG",
          order: 1,
          isRequired: false,
          maxLength: 500,
          showConditions: {
            groups: [
              {
                id: "group1",
                rules: [
                  {
                    id: "rule1",
                    questionId: "question_0", // Will be updated with actual ID
                    operator: "equals",
                    value: "other",
                  },
                ],
                logic: "AND",
              },
            ],
            groupLogic: "OR",
          },
        },
        {
          title: "Quel concurrent avez-vous choisi ?",
          description: "Cela nous aide à comprendre le marché et à améliorer notre offre.",
          type: "TEXT_SHORT",
          order: 2,
          isRequired: false,
          maxLength: 100,
          showConditions: {
            groups: [
              {
                id: "group1",
                rules: [
                  {
                    id: "rule1",
                    questionId: "question_0", // Will be updated with actual ID
                    operator: "equals",
                    value: "competitor",
                  },
                ],
                logic: "AND",
              },
            ],
            groupLogic: "OR",
          },
        },
        {
          title: "Évaluez votre satisfaction globale avec notre service",
          description: "Sur une échelle de 1 à 5, comment évaluez-vous votre expérience ?",
          type: "RATING",
          order: 3,
          isRequired: true,
          minValue: 1,
          maxValue: 5,
          options: [
            { id: "1", label: "1 - Très insatisfait", value: "1" },
            { id: "2", label: "2 - Insatisfait", value: "2" },
            { id: "3", label: "3 - Neutre", value: "3" },
            { id: "4", label: "4 - Satisfait", value: "4" },
            { id: "5", label: "5 - Très satisfait", value: "5" },
          ],
        },
        {
          title: "Quels aspects de notre service avez-vous le plus appréciés ?",
          description: "Sélectionnez tous les éléments qui s'appliquent.",
          type: "MULTIPLE_CHOICE",
          order: 4,
          isRequired: false,
          options: [
            { id: "agents", label: "Qualité des agents IA", value: "agents" },
            { id: "interface", label: "Interface utilisateur", value: "interface" },
            { id: "speed", label: "Rapidité des réponses", value: "speed" },
            { id: "accuracy", label: "Précision des réponses", value: "accuracy" },
            { id: "support", label: "Support client", value: "support" },
            { id: "pricing", label: "Rapport qualité-prix", value: "pricing" },
            { id: "features", label: "Fonctionnalités avancées", value: "features" },
          ],
        },
        {
          title: "Recommanderiez-vous notre service à un ami ou collègue ?",
          description: "Malgré votre annulation, recommanderiez-vous notre service ?",
          type: "YES_NO",
          order: 5,
          isRequired: true,
        },
        {
          title: "Souhaitez-vous recevoir des informations sur nos futures améliorations ?",
          description:
            "Nous pourrions vous contacter si nous ajoutons des fonctionnalités qui répondent à vos besoins.",
          type: "YES_NO",
          order: 6,
          isRequired: false,
        },
        {
          title: "Votre adresse email pour les mises à jour",
          description: "Laissez votre email si vous souhaitez être informé de nos améliorations.",
          type: "EMAIL",
          order: 7,
          isRequired: false,
          showConditions: {
            groups: [
              {
                id: "group1",
                rules: [
                  {
                    id: "rule1",
                    questionId: "question_6", // Will be updated with actual ID
                    operator: "equals",
                    value: true,
                  },
                ],
                logic: "AND",
              },
            ],
            groupLogic: "OR",
          },
        },
        {
          title: "Quand prévoyez-vous de revenir ?",
          description: "Si c'est une pause temporaire, quand pensez-vous reprendre votre abonnement ?",
          type: "DATE",
          order: 8,
          isRequired: false,
          showConditions: {
            groups: [
              {
                id: "group1",
                rules: [
                  {
                    id: "rule1",
                    questionId: "question_0", // Will be updated with actual ID
                    operator: "equals",
                    value: "temporary",
                  },
                ],
                logic: "AND",
              },
            ],
            groupLogic: "OR",
          },
        },
        {
          title: "Commentaires additionnels",
          description: "Partagez tout autre commentaire qui pourrait nous aider à améliorer notre service.",
          type: "TEXT_LONG",
          order: 9,
          isRequired: false,
          maxLength: 1000,
        },
      ]

      // Create questions and update conditional logic with actual question IDs
      interface CreatedQuestion {
        id: string
        originalShowConditions?: ShowConditions
      }

      const createdQuestions: CreatedQuestion[] = []
      for (const questionData of questions) {
        const { showConditions, options, ...questionFields } = questionData

        // Map string types to Prisma enum values
        const questionTypeMap = {
          SINGLE_CHOICE: "SINGLE_CHOICE",
          TEXT_LONG: "TEXT_LONG",
          TEXT_SHORT: "TEXT_SHORT",
          RATING: "RATING",
          MULTIPLE_CHOICE: "MULTIPLE_CHOICE",
          YES_NO: "YES_NO",
          EMAIL: "EMAIL",
          DATE: "DATE",
        } as const

        const question = await prisma.formQuestion.create({
          data: {
            title: questionFields.title,
            description: questionFields.description || null,
            type: questionTypeMap[questionFields.type],
            order: questionFields.order,
            isRequired: questionFields.isRequired,
            minLength: questionFields.minLength || null,
            maxLength: questionFields.maxLength || null,
            minValue: questionFields.minValue || null,
            maxValue: questionFields.maxValue || null,
            formId: cancellationForm.id,
            options: options ? JSON.stringify(options) : undefined,
            showConditions: undefined, // Will be updated after all questions are created
          },
        })

        createdQuestions.push({ ...question, originalShowConditions: showConditions })
      }

      // Update questions with proper conditional logic references
      for (let i = 0; i < createdQuestions.length; i++) {
        const question = createdQuestions[i]
        if (question.originalShowConditions) {
          const updatedConditions: ShowConditions = { ...question.originalShowConditions }

          // Update question IDs in rules
          updatedConditions.groups = updatedConditions.groups.map((group: ConditionalLogicGroup) => ({
            ...group,
            rules: group.rules.map((rule: ConditionalLogicRule) => ({
              ...rule,
              questionId: createdQuestions[parseInt(rule.questionId.split("_")[1])].id,
            })),
          }))

          await prisma.formQuestion.update({
            where: { id: question.id },
            data: {
              showConditions: JSON.stringify(updatedConditions),
            },
          })
        }
      }

      task.stop()
      logger.log("Subscription cancellation form seeded successfully.")
    } else {
      logger.log("Forms already exist, skipping form seeding.")
    }

    //* Migrate user roles if needed
    const task = await startTask({
      name: "Checking for user roles migration",
      successMessage: "User roles migration check completed",
    })

    try {
      // Get all users
      const users = await prisma.user.findMany()

      // Identify users that need migration:
      // 1. Users with role=ADMIN but don't have ADMIN in their roles array
      // 2. Users with other roles that need to be properly reflected in the roles array
      const usersNeedingMigration = users.filter((user) => {
        // Si l'utilisateur a un rôle ADMIN mais n'a pas ADMIN dans son tableau roles
        if (user.role === rolesAsObject.admin && (!user.roles || !user.roles.includes(rolesAsObject.admin))) {
          return true
        }

        // Si l'utilisateur a un rôle spécifique qui n'est pas dans son tableau roles
        if (user.role && (!user.roles || !user.roles.includes(user.role))) {
          return true
        }

        return false
      })

      if (usersNeedingMigration.length > 0) {
        logger.log(`Found ${usersNeedingMigration.length} users that need roles migration`)

        // Update each user that needs migration
        for (const user of usersNeedingMigration) {
          // Ensure we don't lose existing roles
          const existingRoles = user.roles || []

          // Add the legacy role if it's not already in the roles array
          const roles = existingRoles.includes(user.role) ? existingRoles : [...existingRoles, user.role]

          logger.log(`Updating user ${user.id} with roles: ${roles.join(", ")}`)

          // Update user
          await prisma.user.update({
            where: { id: user.id },
            data: { roles },
          })
        }

        logger.log("User roles migration completed successfully!")
      } else {
        logger.log("No users need roles migration")
      }
    } catch (error) {
      logger.error(`Error during user roles migration: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      task.stop()
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
    spinner.stop()
    await prisma.$disconnect()
  }
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    logger.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
